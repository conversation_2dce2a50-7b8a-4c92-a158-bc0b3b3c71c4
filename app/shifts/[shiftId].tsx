import LoadingScreen from "@/components/LoadingView";
import {
  useClockInMutation,
  useClockOutMutation,
  useShiftQuery,
} from "@/generated/graphql";
import { useLocalSearchParams, useRouter } from "expo-router";
import React, { useRef, useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Button,
  Pressable,
} from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
  FadeIn,
} from "react-native-reanimated";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";
import { Card } from "@/components/ui/Card";
import { StatusBar } from "@/components/ui/StatusBar";
import { format } from "date-fns";
import { CameraView, CameraType, useCameraPermissions } from "expo-camera";

export default function ShiftDetails() {
  const router = useRouter();
  const ref = useRef<CameraView>(null);
  const [uri, setUri] = useState<string | null>(null);
  const cardOpacity = useSharedValue(0);
  const buttonScale = useSharedValue(0.95);
  const [isClockedIn, setIsClockedIn] = useState(false);
  const [clockInTime, setClockInTime] = useState<Date | null>(null);
  const [showCamera, setShowCamera] = useState(false);
  const [permission, requestPermission] = useCameraPermissions();

  React.useEffect(() => {
    cardOpacity.value = withTiming(1, { duration: 600 });
    buttonScale.value = withTiming(1, {
      duration: 500,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, []);
  const { shiftId } = useLocalSearchParams<{ shiftId: string }>();
  const { data: shift, isLoading, refetch } = useShiftQuery({ shiftId });
  const { mutateAsync: clockIn } = useClockInMutation();
  const { mutateAsync: clockOut } = useClockOutMutation();

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    opacity: cardOpacity.value,
    transform: [{ scale: buttonScale.value }],
  }));

  // Handle clock in
  const handleClockIn = async (base64Img: string) => {
    try {
      const date = new Date();
      const locationId = shift?.shift.location?.id;
      const shiftId = shift?.shift.id;

      if (!shiftId) throw new Error("Shift ID is missing");

      await clockIn({
        clockInInput: { base64Img, date, locationId, shiftId },
      });

      setClockInTime(date);
      setIsClockedIn(true);
      setShowCamera(false);
    } catch (error) {
      console.error("Clock in failed", error);
      setShowCamera(false);
    }
  };

  // Handle clock out
  const handleClockOut = async () => {
    try {
      const attendanceId = shift;
      const date = new Date();

      if (!shiftId) throw new Error("Shift ID is missing");

      setIsClockedIn(false);
    } catch (error) {
      console.error("Clock out failed", error);
    }
  };

  const formatTime = (date: Date | null): string => {
    if (!date) return "";
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  const handleBackPress = () => {
    router.back();
  };

  const takePicture = async () => {
    try {
      const photo = await ref.current?.takePictureAsync({ base64: true });
      if (photo?.base64) {
        const base64Img = `data:image/jpeg;base64,${photo.base64}`;
        await handleClockIn(base64Img);
      }
    } catch (err) {
      console.error("Failed to take picture", err);
    }
  };

  if (!permission) {
    return null;
  }

  if (!permission?.granted) {
    // Camera permissions are not granted yet.
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.message}>
          We need your permission to show the camera
        </Text>
        <Button onPress={requestPermission} title="grant permission" />
      </View>
    );
  }

  if (showCamera) {
    return (
      <View style={{ flex: 1 }}>
        <CameraView
          style={styles.camera}
          ref={ref}
          facing="front"
          mute={false}
          responsiveOrientationWhenOrientationLocked
        >
          <TouchableOpacity
            onPress={() => setShowCamera(false)}
            style={{ position: "absolute", top: 50, left: 20, zIndex: 99 }}
          >
            <Ionicons name="close" size={32} color="white" />
          </TouchableOpacity>
          <View style={styles.shutterContainer}>
            <Pressable onPress={takePicture}>
              {({ pressed }) => (
                <View
                  style={[styles.shutterBtn, { opacity: pressed ? 0.5 : 1 }]}
                >
                  <View
                    style={[
                      styles.shutterBtnInner,
                      { backgroundColor: "white" },
                    ]}
                  />
                </View>
              )}
            </Pressable>
          </View>
        </CameraView>
      </View>
    );
  }

  //   TODO: Implement shift details
  return (
    <View style={styles.container}>
      <StatusBar barStyle="light" />
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
          <Ionicons name="chevron-back" size={28} color={Colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Shift Details</Text>
      </View>
      <Text style={styles.markAttendance}>Mark Attendance</Text>
      <View>
        <Animated.View
          style={[buttonAnimatedStyle, styles.clockButtonsContainer]}
        >
          <TouchableOpacity
            style={[
              styles.clockButton,
              styles.clockInButton,
              isClockedIn && styles.clockButtonDisabled,
            ]}
            onPress={() => setShowCamera(true)}
            disabled={isClockedIn}
          >
            <Ionicons
              name="time-outline"
              size={20}
              color={isClockedIn ? Colors.textSecondary : Colors.primary}
            />
            <Text
              style={[
                styles.clockButtonText,
                isClockedIn
                  ? styles.clockButtonTextDisabled
                  : styles.clockInButtonText,
              ]}
            >
              {clockInTime ? `In: ${formatTime(clockInTime)}` : "Clock In"}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.clockButton,
              styles.clockOutButton,
              !isClockedIn && styles.clockButtonDisabled,
            ]}
            onPress={handleClockOut}
            disabled={!isClockedIn}
          >
            <Ionicons
              name="exit-outline"
              size={20}
              color={!isClockedIn ? Colors.textSecondary : Colors.error}
            />
            <Text
              style={[
                styles.clockButtonText,
                !isClockedIn
                  ? styles.clockButtonTextDisabled
                  : styles.clockOutButtonText,
              ]}
            >
              Clock Out
            </Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
      <View style={styles.cardContainer}>
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionHeader}>Shift location :</Text>
          <Text style={styles.sectionText}>{shift?.shift.location?.name}</Text>
        </View>
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionHeader}>Assigned:</Text>
          <Text style={styles.sectionText}>
            {shift?.shift.users?.map((user) => user.fullname).join(" , ")}
          </Text>
        </View>
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionHeader}>Shift start time :</Text>
          <Text style={styles.sectionText}>
            {shift?.shift.startDateTime
              ? format(
                  new Date(shift.shift.startDateTime),
                  "dd/MM/yyyy , hh:mm a"
                )
              : "N/A"}
          </Text>
        </View>
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionHeader}>Shift end time :</Text>
          <Text style={styles.sectionText}>
            {shift?.shift.endDateTime
              ? format(
                  new Date(shift.shift.endDateTime),
                  "dd/MM/yyyy , hh:mm a"
                )
              : "N/A"}
          </Text>
        </View>
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionHeader}>Clockin time :</Text>
          <Text style={styles.sectionText}>...</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.lightGray,
  },
  camera: {
    flex: 1,
    width: "100%",
  },
  shutterContainer: {
    position: "absolute",
    bottom: 50,
    left: 0,
    right: 0,
    alignItems: "center",
  },
  shutterBtn: {
    backgroundColor: "transparent",
    borderWidth: 5,
    borderColor: "white",
    width: 85,
    height: 85,
    borderRadius: 45,
    alignItems: "center",
    justifyContent: "center",
  },
  shutterBtnInner: {
    width: 70,
    height: 70,
    borderRadius: 50,
  },
  permissionContainer: { flex: 1, justifyContent: "center" },
  message: {
    textAlign: "center",
    paddingBottom: 10,
  },
  cardContainer: {
    backgroundColor: Colors.cardBackground,
    width: "auto",
    height: "auto",
    margin: 16,
    padding: 16,
    borderRadius: 5,
    elevation: 1,
    gap: 10,
  },
  sectionContainer: {
    gap: 4,
    flexDirection: "row",
    alignItems: "center",
  },
  sectionHeader: {
    fontSize: 16,
    fontWeight: "600",
  },
  sectionText: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.textSecondary,
  },
  markAttendance: {
    fontSize: 20,
    fontWeight: "500",
    paddingLeft: 16,
    marginTop: 16,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    marginRight: 16,
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.white,
    flex: 1,
  },
  card: {
    marginHorizontal: 16,
    marginVertical: 12,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.05)",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.text,
    marginBottom: 16,
  },
  gridContainer: {
    marginBottom: 20,
  },
  dayRow: {
    marginBottom: 12,
  },
  dayRecords: {
    flex: 1,
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 4,
  },
  statusBox: {
    width: 24,
    height: 24,
    borderRadius: 4,
  },
  summaryContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  summaryItem: {
    flex: 1,
    alignItems: "center",
  },
  summaryLabelContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  summaryIndicator: {
    width: 16,
    height: 4,
    borderRadius: 2,
    marginRight: 6,
  },
  summaryLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.text,
  },
  // Clock in/out button styles
  clockButtonsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginHorizontal: 16,
    marginVertical: 12,
    gap: 12,
  },
  clockButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
    backgroundColor: Colors.white,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  clockInButton: {
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  clockOutButton: {
    borderWidth: 1,
    borderColor: Colors.error,
  },
  clockButtonDisabled: {
    backgroundColor: Colors.white,
    borderColor: Colors.lightGray,
    borderWidth: 1,
  },
  clockButtonText: {
    fontWeight: "600",
    fontSize: 14,
  },
  clockButtonTextDisabled: {
    color: Colors.textSecondary,
  },
  clockInButtonText: {
    color: Colors.primary,
  },
  clockOutButtonText: {
    color: Colors.error,
  },
  // Work summary styles
  workSummaryContainer: {
    marginTop: 16,
  },
  workSummaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  workSummaryLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  workSummaryValue: {
    fontSize: 14,
    color: Colors.text,
    fontWeight: "500",
  },
  totalHoursRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    borderBottomWidth: 0,
  },
  totalHoursLabel: {
    fontSize: 16,
    fontWeight: "bold",
    color: Colors.text,
  },
  totalHoursValue: {
    fontSize: 16,
    fontWeight: "bold",
    color: Colors.primary,
  },
});
