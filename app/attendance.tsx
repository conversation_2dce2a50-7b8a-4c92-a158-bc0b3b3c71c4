import {
  AttendanceOverview,
  mockAttendanceData,
} from "@/components/attendance/AttendanceOverview";
import LoadingScreen from "@/components/LoadingView";
import { Colors } from "@/constants/Colors";
import {
  useAttendanceQuery,
  useClockInMutation,
  // useClockOutMutation,
  useShiftsQuery,
} from "@/generated/graphql";
import useDateNow from "@/hooks/useDate";
import { subHours } from "date-fns";
import { StatusBar } from "expo-status-bar";
import React, { useMemo, useRef, useState } from "react";
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Pressable,
  Image,
} from "react-native";
import { CameraView, useCameraPermissions } from "expo-camera";
import { Ionicons } from "@expo/vector-icons";

export default function AttendanceScreen() {
  const { mutateAsync: clockInAsync } = useClockInMutation();
  // const { mutateAsync: clockOutAsync } = useClockOutMutation();

  const dateNow = useDateNow();
  const ref = useRef<CameraView>(null);

  // Camera and face scanning states
  const [showCamera, setShowCamera] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isClockingIn, setIsClockingIn] = useState(false); // Prevent multiple clock-in attempts
  const [permission, requestPermission] = useCameraPermissions();

  // GraphQL queries for shifts and attendance data
  const { data: shifts, isLoading: shiftsLoading } = useShiftsQuery(
    {
      input: {
        startDateTime: subHours(dateNow, 24),
        endDateTime: dateNow,
      },
    },
    { initialData: { shifts: [] } }
  );

  const { data: attendanceData, isLoading: attendanceLoading } =
    useAttendanceQuery(
      {
        attendanceInput: { shiftId: shifts!.shifts?.[0]?.id },
      },
      { initialData: { attendance: [] }, enabled: !!shifts!.shifts?.[0]?.id }
    );

  // Check if user is currently clocked in based on backend data
  const { clockInTime, clockOutTime, isClockedIn } = useMemo(() => {
    if (!attendanceData)
      return { isClockedIn: false, clockInTime: null, clockOutTime: null };
    const isClockedIn = attendanceData.attendance.some(
      (attendance) => !attendance.endTime
    );
    const clockInTime =
      attendanceData.attendance.find((attendance) => !attendance.endTime)
        ?.startTime || null;
    const clockOutTime =
      attendanceData.attendance.find((attendance) => !attendance.endTime)
        ?.endTime || null;

    return { isClockedIn, clockInTime, clockOutTime };
  }, [attendanceData]);

  // If user is clocked in show work hours
  const workHours = useMemo(() => {
    if (!attendanceData) return null;
    const activeAttendance = attendanceData.attendance.find(
      (attendance) => !attendance.endTime
    );
    if (!activeAttendance) return null;
    const diffMs = dateNow.getTime() - activeAttendance.startTime.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    return parseFloat(diffHours.toFixed(2));
  }, [attendanceData, dateNow]);

  // Handle clock in - start camera flow
  const handleClockIn = async () => {
    // Prevent opening camera if already processing or camera is already open
    if (isClockingIn || isProcessing || showCamera || capturedImage) {
      return;
    }

    if (!permission?.granted) {
      const result = await requestPermission();
      if (!result.granted) {
        Alert.alert(
          "Camera Permission Required",
          "Please allow camera access to scan your face for attendance.",
          [{ text: "OK" }]
        );
        return;
      }
    }
    setShowCamera(true);
  };

  // Take picture for face scanning
  const takePicture = async () => {
    try {
      const photo = await ref.current?.takePictureAsync({
        base64: true,
        quality: 0.5,
      });

      if (photo?.base64) {
        const base64Img = `data:image/jpeg;base64,${photo.base64}`;
        setCapturedImage(base64Img);
        setShowCamera(false);
      }
    } catch (error) {
      console.error("Failed to take picture:", error);
      Alert.alert("Error", "Failed to capture your photo. Please try again.", [
        { text: "OK" },
      ]);
    }
  };

  // Submit clock in with captured image
  const handleSubmitClockIn = async () => {
    // Prevent multiple simultaneous clock-in attempts
    if (isClockingIn || isProcessing) {
      return;
    }

    if (!capturedImage) {
      Alert.alert("Error", "No image captured. Please try again.");
      return;
    }

    try {
      setIsProcessing(true);
      setIsClockingIn(true);

      const date = new Date();
      const locationId = shifts?.shifts?.[0]?.location?.id;
      const shiftId = shifts?.shifts?.[0]?.id;

      if (!shiftId || !locationId) {
        Alert.alert("Error", "Shift/Location ID is missing");
        setIsProcessing(false);
        setIsClockingIn(false);
        return;
      }

      await clockInAsync({
        clockInInput: { date, locationId, shiftId, base64Img: capturedImage },
      });

      // Reset states after successful clock in
      setCapturedImage(null);
      setIsProcessing(false);
      setIsClockingIn(false);

      Alert.alert("Success", "Clocked in successfully!", [{ text: "OK" }]);
    } catch (error) {
      console.error("Clock in failed", error);
      setIsProcessing(false);
      setIsClockingIn(false);
      if (error instanceof Error) {
        Alert.alert("Error", error.message, [{ text: "OK" }]);
        return;
      }
    }
  };

  // Handle retake photo
  const handleRetake = () => {
    setCapturedImage(null);
    setShowCamera(true);
  };

  // Handle back press
  const handleBackPress = () => {
    if (showCamera) {
      setShowCamera(false);
    } else if (capturedImage) {
      setCapturedImage(null);
    }
  };

  // Handle clock out
  const handleClockOut = async () => {
    try {
      // TODO: Implement clock out mutation with proper parameters
      // await clockOutAsync({
      //   clockOut: { /* required parameters */ }
      // });
      console.log("Clock out clicked - implement mutation");
    } catch (error) {
      console.error("Clock out failed", error);
    }
  };

  // Show loading screen while fetching data
  if (shiftsLoading || attendanceLoading) {
    return <LoadingScreen />;
  }

  // Show camera view
  if (showCamera) {
    return (
      <View style={cameraStyles.cameraContainer}>
        <CameraView
          style={cameraStyles.camera}
          ref={ref}
          facing="front"
          mute={false}
          responsiveOrientationWhenOrientationLocked
        >
          {/* Back button */}
          <TouchableOpacity
            onPress={handleBackPress}
            style={cameraStyles.backButton}
          >
            <Ionicons name="close" size={32} color="white" />
          </TouchableOpacity>

          {/* Face guide overlay */}
          <View style={cameraStyles.faceGuideContainer}>
            <View style={cameraStyles.faceGuide}>
              <View style={cameraStyles.faceGuideCorner} />
              <View
                style={[cameraStyles.faceGuideCorner, cameraStyles.topRight]}
              />
              <View
                style={[cameraStyles.faceGuideCorner, cameraStyles.bottomLeft]}
              />
              <View
                style={[cameraStyles.faceGuideCorner, cameraStyles.bottomRight]}
              />
            </View>
            <Text style={cameraStyles.guideText}>
              Position your face within the frame for clock in
            </Text>
          </View>

          {/* Capture button */}
          <View style={cameraStyles.shutterContainer}>
            <Pressable onPress={takePicture} disabled={isProcessing}>
              {({ pressed }) => (
                <View
                  style={[
                    cameraStyles.shutterBtn,
                    {
                      opacity: pressed || isProcessing ? 0.5 : 1,
                    },
                  ]}
                >
                  {isProcessing ? (
                    <ActivityIndicator size="small" color="white" />
                  ) : (
                    <View style={cameraStyles.shutterBtnInner} />
                  )}
                </View>
              )}
            </Pressable>
          </View>
        </CameraView>
      </View>
    );
  }

  // Show image preview screen
  if (capturedImage) {
    return (
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            onPress={handleBackPress}
            style={previewStyles.headerBackButton}
          >
            <Ionicons name="arrow-back" size={24} color={Colors.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Preview</Text>
        </View>

        {/* Image Preview */}
        <View style={previewStyles.previewContainer}>
          <View style={previewStyles.imageContainer}>
            <Image
              source={{ uri: capturedImage }}
              style={previewStyles.previewImage}
            />
          </View>

          <Text style={previewStyles.previewTitle}>How does this look?</Text>
          <Text style={previewStyles.previewDescription}>
            Make sure your face is clearly visible for accurate attendance
            tracking.
          </Text>

          {/* Action buttons */}
          <View style={previewStyles.buttonContainer}>
            <TouchableOpacity
              style={previewStyles.retakeButton}
              onPress={handleRetake}
              activeOpacity={0.8}
            >
              <Ionicons name="camera" size={20} color={Colors.primary} />
              <Text style={previewStyles.retakeButtonText}>Retake</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                previewStyles.submitButton,
                isProcessing && previewStyles.submitButtonDisabled,
              ]}
              onPress={handleSubmitClockIn}
              disabled={isProcessing}
              activeOpacity={0.8}
            >
              {isProcessing ? (
                <ActivityIndicator size="small" color={Colors.white} />
              ) : (
                <Ionicons name="checkmark" size={20} color={Colors.white} />
              )}
              <Text style={previewStyles.submitButtonText}>
                {isProcessing ? "Processing..." : "Clock In"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      <View style={styles.header}>
        <Text style={styles.title}>Attendance</Text>
        <Text style={styles.subtitle}>Track your attendance records</Text>
      </View>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Attendance Overview Component */}
        <AttendanceOverview
          data={mockAttendanceData}
          isClockedIn={isClockedIn}
          clockInTime={clockInTime}
          clockOutTime={clockOutTime}
          workHours={workHours}
          onClockIn={handleClockIn}
          onClockOut={handleClockOut}
        />

        {/* Additional attendance content can be added here */}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F0F0", // Very light grey background
  },
  header: {
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 10,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: 16,
  },
});

// Camera styles
const cameraStyles = StyleSheet.create({
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  backButton: {
    position: "absolute",
    top: 50,
    left: 20,
    zIndex: 99,
    padding: 10,
  },
  faceGuideContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 40,
  },
  faceGuide: {
    width: 250,
    height: 300,
    position: "relative",
    marginBottom: 30,
  },
  faceGuideCorner: {
    position: "absolute",
    width: 30,
    height: 30,
    borderColor: Colors.white,
    borderWidth: 3,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  guideText: {
    color: Colors.white,
    fontSize: 16,
    textAlign: "center",
    fontWeight: "500",
  },
  shutterContainer: {
    position: "absolute",
    bottom: 50,
    left: 0,
    right: 0,
    alignItems: "center",
  },
  shutterBtn: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 4,
    borderColor: Colors.white,
  },
  shutterBtnInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.white,
  },
});

// Preview styles
const previewStyles = StyleSheet.create({
  headerBackButton: {
    padding: 8,
    marginRight: 16,
  },
  previewContainer: {
    flex: 1,
    padding: 20,
    alignItems: "center",
  },
  imageContainer: {
    width: 250,
    height: 300,
    borderRadius: 20,
    overflow: "hidden",
    marginTop: 40,
    marginBottom: 30,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  previewImage: {
    width: "100%",
    height: "100%",
  },
  previewTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.text,
    marginBottom: 12,
    textAlign: "center",
  },
  previewDescription: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: "center",
    marginBottom: 40,
    paddingHorizontal: 20,
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: "row",
    gap: 16,
    paddingHorizontal: 20,
  },
  retakeButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    backgroundColor: Colors.white,
    borderWidth: 2,
    borderColor: Colors.primary,
    gap: 8,
  },
  retakeButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.primary,
  },
  submitButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    backgroundColor: Colors.primary,
    gap: 8,
  },
  submitButtonDisabled: {
    backgroundColor: Colors.primaryLight,
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.white,
  },
});
